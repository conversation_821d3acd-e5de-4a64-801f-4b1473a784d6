from unicodedata import name
from datetime import datetime
from django.shortcuts import render, redirect
from django.http import HttpResponse, JsonResponse
from pathlib import Path
from django.conf import settings
import pandas as pd
import geopandas as gpd
import csv
import os
import json
from django.contrib.auth import authenticate, login, logout
# permission group User model
from django.contrib.auth.models import Group, User, Permission

# ใช้กับ login_required
from django.contrib.auth.decorators import login_required
# import models
from .models import *
# import django.contrib.gis.geos
from django.contrib.gis.geos import Point, Polygon, LineString
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.db.models import Transform
from django.contrib.gis.geos import GEOSGeometry
from django.contrib.gis.db.models.functions import AsGeoJSON
from django.views.decorators.http import require_http_methods
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
# database connection
from django.db import connection



def Add_deed_parcel(request):

    return render(request, 'add_deed_parcel.html')

def Editor_page(request, id_parcel_rent):
    try:
        # query Time_line_asset_rent model id
        time_line_asset_rent = Time_line_asset_rent.objects.filter(parcel_rent=id_parcel_rent).latest('date_add')
        # get id time_line_asset_rent
        id_time_line = time_line_asset_rent.id
        # context
        context = {
            'id_time_line': id_time_line
        }
    except:
        # context ไม่มีข้อมูล ต้องออกเป็น ตัวเลข 0
        context = {
            'id_time_line': 0
        }
    print(context)
    return render(request, 'editor_page.html', context)

def Job_list(request):
    return render(request, 'job_list.html')


def Test(request):
    # Ensure the user is authenticated to access this view
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Unauthorized access'}, status=401)

    # Query the Profile model
    # This will automatically join with the User model due to the OneToOne relationship
    # The related Type_user model is also fetched
    profiles = Profile.objects.select_related('user', 'type_user').all()

    # Prepare data for JsonResponse
    data = []
    for profile in profiles:
        user_data = {
            'username': profile.user.username,
            'email': profile.user.email,
            'type_user': profile.type_user.type_name if profile.type_user else None
        }
        data.append(user_data)

    return JsonResponse(data, safe=False)


def LayoutA_3(request, id_timeline = None):
    # Example usage of id_time_line, adjust according to your needs
    # context = {'id_time_line': id_time_line}
    return render(request, 'layout_a3.html')

@login_required
def Index(request):
    # get user and profile
    user = request.user
    profile = Profile.objects.get(user=user)

    # get type_user
    type_user = profile.type_user
    # type_user to string
    type_user = str(type_user)
    if type_user == 'ผู้อำนวยการ':
        return render(request, 'manager_cpb.html')
    else:
        return render(request, 'main.html')




def Login(request):
    context = {} #สิ่งที่จะแนบไป dictionary
    if request.method == 'POST':
        data = request.POST.copy()
        username = data.get('username')
        password = data.get('password')
        # context['user'] = username
        # context['pass'] = password
        # context['test_sent'] = 'TEST ARM'
        try:
            user = authenticate(username=username, password=password)
            login(request, user)
        except:
            context['message'] = 'user หรือ password อาจไม่ถูกต้อง'

    return render(request, 'sign-in.html', context)

def Signup(request):
    context = {} #สิ่งที่จะแนบไป dictionary
    if request.method == 'POST':
        data = request.POST.copy()
        username = data.get('username')
        password = data.get('password')

    return render(request, 'sign-up.html')

# รูปแปลงโฉนดที่ดิน
@login_required
def Ajax_parcel_deed(request, status_survey_dol):
    queryset = Parcel_deed.objects.filter(status_survey_dol=status_survey_dol)
    geojson = {
        'type': 'FeatureCollection',
        'features': [
            {
                'type': 'Feature',
                'geometry': json.loads(x.geom.geojson),
                'properties': {
                    'id': x.id
                }
            } for x in queryset
        ]
    }
    # print(geojson)
    # return JsonResponse(geojson)

    return HttpResponse(json.dumps(geojson), content_type='application/json')

# รายละเอียด โฉนดที่ดิน
@login_required
def Ajax_detail_parcel_deed(request, id_parcel_deed):

    # get data model many to one
    # sql = "select * from app_asset_deed as ad where ad.parcel_deed_id = {}".format(id_parcel_deed)
    detail = Asset_deed.objects.get(parcel_deed_id=id_parcel_deed)
    # print(detail.rai_number)
    context = {
        'deed_no': detail.deed_no,
        'land_no': detail.land_no,
        'book_no': detail.book_no,
        'page_no': detail.page_no,
        'survey_page_number': detail.survey_page_number,
        'tam_code': detail.tam_code,
        'amp_code': detail.amp_code,
        'prov_code': detail.prov_code,
        'rai_number': detail.rai_number,
        'ngarn_number': detail.ngarn_number,
        'sqw_number': detail.sqw_number,
        'responsibility': detail.responsibility,
        'date_add': detail.date_add
    } #สิ่งที่จะแนบไป dictionary

    return JsonResponse(context, safe=False)
    # return HttpResponse(detail_list, content_type='application/json; charset=utf-8')

# รูปแปลงเช่า
@login_required #redirect ไปหน้า Login เมื่อยังไม้ได้เข้าสู่ระบบ หน้า Index
def Ajax_parcel_rent(request):
    # data post copy
    data = request.POST.copy()
    id_parcel_rent = data.get('id_parcel_rent')
    # print(id_parcel_rent)
    if id_parcel_rent != None:
        queryset = Parcel_rent.objects.filter(id=id_parcel_rent)

    else:
        queryset = Parcel_rent.objects.all()

    geojson = {
        'type': 'FeatureCollection',
        'features': [
            {
                'type': 'Feature',
                'geometry': json.loads(x.geom.geojson),
                'properties': {
                    'id': x.id
                }
            } for x in queryset
        ]
    }
    return HttpResponse(json.dumps(geojson), content_type='application/json')

# รายละเอียดแปลงเช่า
@login_required #redirect ไปหน้า Login เมื่อยังไม้ได้เข้าสู่ระบบ หน้า Index
def Ajax_detail_parcel_rent(request, id_parcel_rent):
    # get data model many to one
    id_asset = Time_line_asset_rent.objects.get(parcel_rent=id_parcel_rent)
    # print(id_asset.asset_rent_id)
    # get detail from asset_rent model
    detail = Asset_rent.objects.get(id=id_asset.asset_rent_id)
    context = {
        'id_asset': id_asset.asset_rent_id,
        'name_rent': detail.name_rent,
        'prom_type': detail.prom_type,
        'rate_type': detail.rate_type,
        'number_parcel_rent': detail.number_parcel_rent,
        'talk_tunbon': detail.talk_tunbon,
        'tumbon': detail.tumbon,
        'amphoe': detail.amphoe,
        'province': detail.province,
        'deed_no': detail.deed_no,
        'land_no': detail.land_no,
        'area_rent_sqwa': detail.area_rent_sqwa,
        'sv_datetime': detail.sv_datetime,
    }
    return JsonResponse(context, safe=False)






###################################หน้า Editor###################################
# รูปสิ่งปลูกสร้าง
@login_required
def Ajax_building(request):
    context = {} #สิ่งที่จะแนบไป dictionary
    sql = "select json_build_object('type', 'FeatureCollection','features', json_agg(ST_AsGeoJSON(b.*)::json)) as geojson "
    sql += "from app_geom_building as b"

    #query parcel_deed
    with connection.cursor() as cursor:
        cursor.execute(sql)
        parcel_deed = cursor.fetchone()
    geojson = json.dumps(parcel_deed[0])

    return HttpResponse(geojson, content_type='application/json; charset=utf-8')


# รูปสิ่งปลูกสร้าง บนแปลงเช่า
@login_required
@require_http_methods(["POST"])
def Ajax_building_on_parcel(request):
    # Get POST data
    data = request.POST.copy()
    # Get the parcel ID from the data
    id_parcel_rent = data.get('id_parcel_rent')

    if not id_parcel_rent:
        return HttpResponse(status=400, content='Missing parcel ID')

    try:
        # Use parameters to safely pass user input
        sql = "SELECT ST_AsText(geom) FROM app_parcel_rent WHERE id = %s;"
        with connection.cursor() as cursor:
            cursor.execute(sql, [id_parcel_rent])
            geom = cursor.fetchone()

        if geom is None:
            return HttpResponse(status=404, content='Parcel not found')

        geom_rent = geom[0]
        # print(geom_rent)

        sql = """
        SELECT json_build_object(
            'type',       'FeatureCollection',
            'features',   json_agg(
                json_build_object(
                    'type',       'Feature',
                    'geometry',   ST_AsGeoJSON(bu.geom)::json,
                    'properties', json_build_object(
                        'id', bu.id
                    )
                )
            )
        )
        FROM app_geom_building AS bu
        WHERE ST_Intersects(ST_GeomFromText(%s, 4326), bu.geom);
        """
        with connection.cursor() as cursor:
            cursor.execute(sql, [geom_rent])
            shp_bu_geojson = cursor.fetchone()[0]  # Directly fetch the GeoJSON representation

        if shp_bu_geojson is None:
            return HttpResponse(status=404, content='No intersecting buildings found')

        return HttpResponse(json.dumps(shp_bu_geojson), content_type='application/json; charset=utf-8')
    except Exception as e:
        # Log the error for debugging
        print(e)
        return HttpResponse(status=500, content='Internal Server Error')

# แปลงข้างเคียง บนแปลงเช่า
@login_required
@require_http_methods(["POST"])
def Ajax_side_parcel(request):
    # Get POST data
    data = request.POST.copy()
    # Get the parcel ID from the data
    id_parcel_rent = data.get('id_parcel_rent')
    offset_distance = data.get('offset_distance')

    if not id_parcel_rent:
        return HttpResponse(status=400, content='Missing parcel ID')

    try:
        # Use parameters to safely pass user input
        sql = "SELECT ST_AsText(ST_Transform(ST_Buffer(ST_Transform(geom,32647),%s),4326)) FROM app_parcel_rent WHERE id = %s;"
        with connection.cursor() as cursor:
            cursor.execute(sql, [offset_distance, id_parcel_rent])
            geom = cursor.fetchone()

        if geom is None:
            return HttpResponse(status=404, content='Parcel not found')

        geom_rent = geom[0]
        # print(geom_rent)

        sql = """
        SELECT json_build_object(
            'type',       'FeatureCollection',
            'features',   json_agg(
                json_build_object(
                    'type',       'Feature',
                    'geometry',   ST_AsGeoJSON(pr.geom)::json,
                    'properties', json_build_object(
                        'id', pr.id
                    )
                )
            )
        )
        FROM app_parcel_rent AS pr
        WHERE ST_Intersects(ST_GeomFromText(%s, 4326), pr.geom) AND pr.id != %s;
        """
        with connection.cursor() as cursor:
            cursor.execute(sql, [geom_rent, id_parcel_rent])
            side_parcel_geojson = cursor.fetchone()[0]  # Directly fetch the GeoJSON representation

        if side_parcel_geojson is None:
            return HttpResponse(status=404, content='No intersecting buildings found')

        return HttpResponse(json.dumps(side_parcel_geojson), content_type='application/json; charset=utf-8')
    except Exception as e:
        # Log the error for debugging
        print(e)
        return HttpResponse(status=500, content='Internal Server Error')

# ขีดเขตแยก
@login_required
@require_http_methods(["POST"])
def Side_plot_lines_views(request):
    data = request.POST.copy()
    id_time_line = data.get('id_time_line')
    type_query = data.get('type_query')

    # add ขีดเขตแยก
    if type_query == 'add':
        wktString = data.get('wktString')


        try:
            # Create and save the new boundary line
            new_line = Side_plot_lines(
                geom=f"SRID=4326;{wktString}",
                time_line_asset_rent_id=id_time_line,
                date_add=datetime.now()
            )
            new_line.save()

            # Retrieve the most recently added boundary line for the given timeline ID
            latest_line = Side_plot_lines.objects.filter(time_line_asset_rent_id=id_time_line).order_by('-id').first()
            response_data = {
                "type": "FeatureCollection",
                "features": [{
                    "type": "Feature",
                    "geometry": json.loads(latest_line.geom.geojson),
                    "properties": {
                        "id": latest_line.id
                    }
                }]
            }

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

        return JsonResponse(response_data)

    # แสดง ขีดเขตแยก
    elif type_query == 'show':
        # get geom as geojson with sql
        sql = """
        SELECT json_build_object(
            'type',       'FeatureCollection',
            'features',   json_agg(
                json_build_object(
                    'type',       'Feature',
                    'geometry',   ST_AsGeoJSON(pr.geom)::json,
                    'properties', json_build_object(
                        'id', pr.id
                    )
                )
            )
        )
        FROM app_side_plot_lines AS pr
        WHERE pr.time_line_asset_rent_id = %s;
        """
        with connection.cursor() as cursor:
            cursor.execute(sql, [id_time_line])
            side_plot_lines_geojson = cursor.fetchone()[0]
        return HttpResponse(json.dumps(side_plot_lines_geojson), content_type='application/json; charset=utf-8')

    # แก้ไข ขีดเขตแยก
    elif type_query == 'edit':
        wktString = data.get('wktString')
        id_side_plot_lines = data.get('id_side_plot_lines')
        # update data model Side_plot_lines with sql
        sql = "UPDATE app_side_plot_lines SET geom = ST_GeomFromText(%s, 4326) WHERE id = %s;"
        with connection.cursor() as cursor:
            cursor.execute(sql, [wktString, id_side_plot_lines])
        return HttpResponse('update')

    # ลบ ขีดเขตแยก
    elif type_query == 'delete':
        id_side_plot_lines = data.get('id_side_plot_lines')
        # delete data model Side_plot_lines with sql
        sql = "DELETE FROM app_side_plot_lines WHERE id = %s;"
        with connection.cursor() as cursor:
            cursor.execute(sql, [id_side_plot_lines])
        return HttpResponse('delete')

# END ขีดเขตแยก

# แสดงข้อมูลกรรมสิทธิ์เช่า
@login_required
@require_http_methods(["POST"])
def Show_asset_rent(request):
    # Get POST data
    data = request.POST.copy()

    # Get the parcel ID from the data
    id_parcel_rent = data.get('id_parcel_rent')

    # check id_parcel_rent on Time_line_asset_rent query
    try:
        # get data model many to one get date_add last
        id_asset = Time_line_asset_rent.objects.get(parcel_rent=id_parcel_rent)
        # print(id_asset.asset_rent_id)
        # get detail from asset_rent model
        detail = Asset_rent.objects.get(id=id_asset.asset_rent_id)
        context = {
            'id_asset': id_asset.asset_rent_id,
            'name_rent': detail.name_rent,
            'prom_type': detail.prom_type,
            'rate_type': detail.rate_type,
            'number_parcel_rent': detail.number_parcel_rent,
            'talk_tunbon': detail.talk_tunbon,
            'tumbon': detail.tumbon,
            'amphoe': detail.amphoe,
            'province': detail.province,
            'deed_no': detail.deed_no,
            'land_no': detail.land_no,
            'area_rent_sqwa': detail.area_rent_sqwa,
            'sv_datetime': detail.sv_datetime.strftime('%d-%m-%Y') if detail.sv_datetime else None,
        }
        # print(context)
        return JsonResponse(context, safe=False)
    except:
        # print('no data')
        return HttpResponse('no data')

# เพิ่ม หรือ อัพเดท ข้อมูลกรรมสิทธิ์เช่า
@login_required
@require_http_methods(["POST"])
def Add_asset_rent(request):
    # Get POST data
    data = request.POST.copy()
    # print(data)
    # Get the parcel ID from the data
    id_parcel_rent = data.get('id_parcel_rent')
    name_rent = data.get('name_rent')
    prom_type = data.get('prom_type')
    rate_type = data.get('rate_type')
    number_parcel_rent = data.get('number_parcel_rent')
    talk_tunbon = data.get('talk_tunbon')
    tumbon = data.get('tumbon')
    amphoe = data.get('amphoe')
    province = data.get('province')
    deed_no = data.get('deed_no')
    land_no = data.get('land_no')
    area_rent_sqwa = data.get('area_rent_sqwa')
    # 24-11-2021 to timestamp
    sv_datetime = data.get('sv_datetime')
    # check id_asset from Time_line_asset_rent query
    try:
        id_asset = Time_line_asset_rent.objects.get(parcel_rent=id_parcel_rent)
        # print(id_asset.asset_rent_id)
        # update data model Asset_rent
        asset_rent = Asset_rent.objects.get(id=id_asset.asset_rent_id)
        asset_rent.name_rent = name_rent
        asset_rent.prom_type = prom_type
        asset_rent.rate_type = rate_type
        asset_rent.number_parcel_rent = number_parcel_rent
        asset_rent.talk_tunbon = talk_tunbon
        asset_rent.tumbon = tumbon
        asset_rent.amphoe = amphoe
        asset_rent.province = province
        asset_rent.deed_no = deed_no
        asset_rent.land_no = land_no
        asset_rent.area_rent_sqwa = area_rent_sqwa
        # 24-11-2021 format to datetimefield
        asset_rent.sv_datetime = datetime.strptime(sv_datetime, '%d-%m-%Y').date()


        asset_rent.save()
        return HttpResponse('update')
    except:
        # insert data model Asset_rent
        asset_rent = Asset_rent()
        asset_rent.name_rent = name_rent
        asset_rent.prom_type = prom_type
        asset_rent.rate_type = rate_type
        asset_rent.number_parcel_rent = number_parcel_rent
        asset_rent.talk_tunbon = talk_tunbon
        asset_rent.tumbon = tumbon
        asset_rent.amphoe = amphoe
        asset_rent.province = province
        asset_rent.deed_no = deed_no
        asset_rent.land_no = land_no
        asset_rent.area_rent_sqwa = area_rent_sqwa
        asset_rent.sv_datetime = datetime.strptime(sv_datetime, '%d-%m-%Y').date()
        asset_rent.save()
        # insert data model Time_line_asset_rent
        time_line_asset_rent = Time_line_asset_rent()
        time_line_asset_rent.parcel_rent_id = id_parcel_rent
        time_line_asset_rent.asset_rent_id = asset_rent.id
        # used field
        time_line_asset_rent.used = True
        time_line_asset_rent.type_sv_job = 'สอบเขต'
        time_line_asset_rent.date_add = datetime.now()
        time_line_asset_rent.save()
        return HttpResponse('insert')


# หมุดเขต
@login_required
@require_http_methods(["POST"])
def Ajax_pin_rents(request):
    data = request.POST.copy()
    id_time_line = data.get('id_time_line')
    type_query = data.get('type_query')

    if type_query == 'get_vertex':
        # Get parcel_rent geometry from Time_line_asset_rent
        id_time_line = Time_line_asset_rent.objects.get(id=id_time_line)
        parcel_rent = Parcel_rent.objects.get(id=id_time_line.parcel_rent_id)

        # SQL to fetch vertices as GeoJSON
        sql = """
        SELECT ST_AsGeoJSON(ST_Collect(array_agg((dp).geom)))
        FROM (
            SELECT ST_DumpPoints(ST_ExteriorRing(geom)) as dp
            FROM app_parcel_rent
            WHERE id = %s
        ) as subquery;
        """

        with connection.cursor() as cursor:
            cursor.execute(sql, [parcel_rent.id])
            result = cursor.fetchone()
            if result and result[0]:
                geojson_result = json.loads(result[0])
            else:
                return JsonResponse({'error': 'No geometry data found'}, status=404)
        # print(geojson_result)
        # insert data model Point_rents
        for index, vertex in enumerate(geojson_result['coordinates']):
            # continue loop แรก ให้ข้าม
            if index == 0:
                continue
            # insert data model Point_rents
            point_rent = Point_rents()
            point_rent.geom = GEOSGeometry(f"POINT({vertex[0]} {vertex[1]})", srid=4326)
            point_rent.time_line_asset_rent_id = id_time_line.id
            point_rent.save()
        # get data model Point_rents
        point_rents = Point_rents.objects.filter(time_line_asset_rent_id=id_time_line.id)


        # Create a GeoJSON object for Leaflet
        point_rents_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id
                    }
                } for x in point_rents
            ]
        }

        # Ensure we use parameters properly to avoid SQL injection
        template = """
            SELECT *
            FROM app_point_label AS pl
            WHERE ST_Intersects(
                ST_Buffer(ST_Transform(ST_GeomFromText(%s, 4326), 32647), 3.1),
                ST_Buffer(ST_Transform(pl.geom, 32647), 0.001)
            );
        """

        for point_rent in point_rents:
            # Convert point coordinates to WKT format
            wkt_point_rent = f"POINT({point_rent.geom.x} {point_rent.geom.y})"

            # Execute the query with proper parameter binding
            with connection.cursor() as cursor:
                cursor.execute(template, [wkt_point_rent])
                result = cursor.fetchone()

                if result:
                    # insert to table Point_texts
                    point_text = Point_texts()
                    point_text.point_rents_id = point_rent.id
                    point_text.contents = result[2]
                    # geometry data
                    point_text.geom = result[1]
                    point_text.save()
                else:
                    # insert to table Point_texts
                    point_text = Point_texts()
                    point_text.point_rents_id = point_rent.id
                    point_text.contents = 'ไม่พบข้อมูล'
                    # geometry data
                    point_text.geom = result[1]
                    point_text.save()

        # get data model Point_texts from Point_rents
        point_texts = Point_texts.objects.filter(point_rents_id__in=point_rents)
        # Create a GeoJSON object for Leaflet
        point_label_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "contents": x.contents
                    }
                } for x in point_texts
            ]
        }

        return JsonResponse({
            'point_rents': point_rents_geojson,
            'point_label': point_label_geojson

        })
    elif type_query == 'get_pin_rents':
        # get data model Point_rents
        point_rents = Point_rents.objects.filter(time_line_asset_rent_id=id_time_line)
        # Create a GeoJSON object for Leaflet
        point_rents_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id
                    }
                } for x in point_rents
            ]
        }
        # get data model Point_texts
        point_texts = Point_texts.objects.filter(point_rents_id__in=point_rents)
        # Create a GeoJSON object for Leaflet
        point_label_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "contents": x.contents
                    }
                } for x in point_texts
            ]
        }
        return JsonResponse({
            'point_rents': point_rents_geojson,
            'point_label': point_label_geojson
        })
    elif type_query == 'edit_pin_label':
        # Get POST data
        data = request.POST.copy()
        id_pin_label = data.get('id_pin_label')
        lat = data.get('lat')
        lng = data.get('lng')
        contents = data.get('contents')
        # update data model Point_texts
        point_text = Point_texts.objects.get(id=id_pin_label)
        point_text.contents = contents
        point_text.geom = GEOSGeometry(f"POINT({lng} {lat})", srid=4326)
        point_text.save()
        return HttpResponse('update')
    elif type_query == 'delete_pin_label':
        # Get POST data
        data = request.POST.copy()
        id_pin_label = data.get('id_pin_label')
        # get point_rents_id from Point_texts
        point_text = Point_texts.objects.get(id=id_pin_label)
        point_rents_id = point_text.point_rents_id
        # delete data model Point_texts
        Point_texts.objects.filter(id=id_pin_label).delete()
        # delete data model Point_rents
        Point_rents.objects.filter(id=point_rents_id).delete()
        # return point_rents_id
        return HttpResponse(point_rents_id)
# End หมุดเขต

# อัพเดท scale map
@login_required
@require_http_methods(["POST"])
def Scale_map(request):
    # Get POST data
    data = request.POST.copy()
    id_time_line = data.get('id_time_line')
    scale_map = data.get('scale_map')
    type_query = data.get('type_query')
    if type_query == 'edit':
        # update data model Time_line_asset_rent
        time_line_asset_rent = Time_line_asset_rent.objects.get(id=id_time_line)
        time_line_asset_rent.scale_rent = scale_map
        time_line_asset_rent.save()
        return HttpResponse('update_scale_map')
    elif type_query == 'get':
        # get data model Time_line_asset_rent
        time_line_asset_rent = Time_line_asset_rent.objects.get(id=id_time_line)
        scale_map = time_line_asset_rent.scale_rent
        return HttpResponse(scale_map)
# End อัพเดท scale map


# วงหมุดเลขแปลง
@login_required
@require_http_methods(["POST"])
def Ajax_parcel_number(request):
    # Get POST data
    data = request.POST.copy()
    id_time_line = data.get('id_time_line')
    type = data.get('type')
    type_query = data.get('type_query')

    if type_query == 'add':
            # get data model many to one
            id_time_line_obj = get_object_or_404(Time_line_asset_rent, id=id_time_line)
            # get scale rent from Time_line_asset_rent
            scale_rent = id_time_line_obj.scale_rent
            # distance_buffer
            distance_buffer = (1*scale_rent)/100
            # print(scale_rent)
            # get data model Parcel_rent
            parcel_rent = get_object_or_404(Parcel_rent, id=id_time_line_obj.parcel_rent.id)
            # get geom from Parcel_rent
            geom_rent = parcel_rent.geom
            # geom_rent to wkt
            geom_rent = geom_rent.wkt
            # get data model Text_map
            text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line)
            # check count data model Text_map
            count_text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line).count()
            # print('count',count_text_map)
            if count_text_map == 0:
                # intersect data model parcel_rent with buffer
                sql = """
                SELECT ST_AsGeoJSON(pr.geom) as geom, pr.id as id
                FROM app_parcel_rent as pr
                WHERE ST_Intersects(
                    ST_Transform(
                        ST_Buffer(
                            ST_Transform(ST_GeomFromText(%s, 4326), 32647), %s
                        ), 4326
                    ), pr.geom
                );
                """
                with connection.cursor() as cursor:
                    cursor.execute(sql, [geom_rent, distance_buffer])
                    parcel_number = cursor.fetchall()

                # loop data model parcel_number
                for i in parcel_number:
                    try:
                        # get id asset_rent from Time_line_asset_rent
                        id_asset_rent = Time_line_asset_rent.objects.get(parcel_rent=i[1])
                        # get data model Asset_rent
                        asset_rent = Asset_rent.objects.get(id=id_asset_rent.asset_rent_id)
                        print(asset_rent.number_parcel_rent)
                        # get data model parcel_rent
                        parcel_rent = Parcel_rent.objects.get(id=i[1])
                        # get centroid parcel_rent from geom
                        centroid = parcel_rent.geom.centroid
                        # print(centroid)
                        # insert data model Text_map
                        text_map = Text_map()
                        text_map.time_line_asset_rent_id = id_time_line
                        text_map.content = asset_rent.number_parcel_rent
                        text_map.type = type
                        text_map.geom = centroid
                        text_map.save()

                    except:
                        continue

                # get data model Text_map as geojson
                text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line)
                text_map_geojson = {
                    "type": "FeatureCollection",
                    "features": [
                        {
                            "type": "Feature",
                            "geometry": json.loads(x.geom.geojson),
                            "properties": {
                                "id": x.id,
                                "content": x.content
                            }
                        } for x in text_map
                    ]
                }
                return JsonResponse(text_map_geojson)

            elif count_text_map > 0:
                # delete data model Text_map
                Text_map.objects.filter(time_line_asset_rent_id=id_time_line).delete()
                # intersect data model parcel_rent with buffer
                sql = """
                SELECT ST_AsGeoJSON(pr.geom) as geom, pr.id as id
                FROM app_parcel_rent as pr
                WHERE ST_Intersects(
                    ST_Transform(
                        ST_Buffer(
                            ST_Transform(ST_GeomFromText(%s, 4326), 32647), %s
                        ), 4326
                    ), pr.geom
                );
                """
                with connection.cursor() as cursor:
                    cursor.execute(sql, [geom_rent, distance_buffer])
                    parcel_number = cursor.fetchall()

                # loop data model parcel_number
                for i in parcel_number:
                    try:
                        # get id asset_rent from Time_line_asset_rent
                        id_asset_rent = Time_line_asset_rent.objects.get(parcel_rent=i[1])
                        # get data model Asset_rent
                        asset_rent = Asset_rent.objects.get(id=id_asset_rent.asset_rent_id)
                        print(asset_rent.number_parcel_rent)
                        # get data model parcel_rent
                        parcel_rent = Parcel_rent.objects.get(id=i[1])
                        # get centroid parcel_rent from geom
                        centroid = parcel_rent.geom.centroid
                        # print(centroid)
                        # insert data model Text_map
                        text_map = Text_map()
                        text_map.time_line_asset_rent_id = id_time_line
                        text_map.content = asset_rent.number_parcel_rent
                        text_map.geom = centroid
                        text_map.type = 'no_parcel' #ประเภทหมายเลขแปลง
                        text_map.save()

                    except:
                        continue

                # get data model Text_map as geojson
                text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line)
                text_map_geojson = {
                    "type": "FeatureCollection",
                    "features": [
                        {
                            "type": "Feature",
                            "geometry": json.loads(x.geom.geojson),
                            "properties": {
                                "id": x.id,
                                "content": x.content
                            }
                        } for x in text_map
                    ]
                }
                return JsonResponse(text_map_geojson)
    elif type_query == 'get':
        # get data model Text_map and type = 'no_parcel'
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type='no_parcel')
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "content": x.content,
                        "type": x.type
                    }
                } for x in text_map
            ]
        }
        return JsonResponse(text_map_geojson)
    elif type_query == 'edit':
        # Get POST data
        data = request.POST.copy()
        id_text_map = data.get('id_text_map')
        id_time_line = data.get('id_time_line')
        lat = data.get('lat')
        lng = data.get('lng')
        contents = data.get('content')
        # update text_map where id_text_map
        text_map = Text_map.objects.get(id=id_text_map)
        text_map.content = contents
        text_map.geom = GEOSGeometry(f"POINT({lng} {lat})", srid=4326)
        text_map.save()
        return HttpResponse('update')
    elif type_query == 'delete':
        # Get POST data
        data = request.POST.copy()
        id_text_map = data.get('id_text_map')
        # delete data model Text_map
        Text_map.objects.filter(id=id_text_map).delete()
        return HttpResponse('delete')







# End วงหมุดเลขแปลง

################################### End หน้า Editor###################################

###################################  หน้า Layout###################################
@login_required
def Ajax_layout_render_parcel(request):
    # Get POST data
    data = request.POST.copy()
    # print(data)
    # Get the parcel ID from the data
    id_timeline = data.get('id_timeline')
    # print(id_timeline)
    # get data model many to one
    id_timeline = Time_line_asset_rent.objects.filter(id=id_timeline).latest('date_add')
    # get Parcel_rent
    parcel_rent = Parcel_rent.objects.filter(id=id_timeline.parcel_rent_id)
    # geojson
    geojson = {
        'type': 'FeatureCollection',
        'features': [
            {
                'type': 'Feature',
                'geometry': json.loads(x.geom.geojson),
                'properties': {
                    'id': x.id
                }
            } for x in parcel_rent
        ]
    }
    # print(geojson)
    return HttpResponse(json.dumps(geojson), content_type='application/json')
@login_required
def Ajax_layout_render_detail(request):
    # Get POST data
    data = request.POST.copy()
    # print(data)
    # Get the parcel ID from the data
    id_timeline = data.get('id_timeline')
    # print(id_timeline)
    # get data model many to one
    id_timeline = Time_line_asset_rent.objects.filter(id=id_timeline).latest('date_add')
    # get Asset_rent
    asset_rent = Asset_rent.objects.filter(id=id_timeline.asset_rent_id)
    # print(asset_rent)
    # get detail from asset_rent model
    detail = Asset_rent.objects.get(id=id_timeline.asset_rent_id)
    context = {
        'id_asset': id_timeline.asset_rent_id,
        'name_rent': detail.name_rent,
        'prom_type': detail.prom_type,
        'rate_type': detail.rate_type,
        'number_parcel_rent': detail.number_parcel_rent,
        'talk_tunbon': detail.talk_tunbon,
        'tumbon': detail.tumbon,
        'amphoe': detail.amphoe,
        'province': detail.province,
        'deed_no': detail.deed_no,
        'land_no': detail.land_no,
        'area_rent_sqwa': detail.area_rent_sqwa,
        'sv_datetime': detail.sv_datetime.strftime('%d-%m-%Y') if detail.sv_datetime else None,
    }
    # print(context)
    return JsonResponse(context, safe=False)


################################### End หน้า Layout###################################


# ระบบค้นหาแปลงที่ดินโฉนด และแปลงเช่า
@login_required
def Search_ajax(request, search_text=None):

    # filter like data model Asset_deed
    asset_deed_result = Asset_deed.objects.filter(deed_no__icontains=search_text ) | \
        Asset_deed.objects.filter(land_no__icontains=search_text ) | \
        Asset_deed.objects.filter(tam_code__icontains=search_text ) | \
        Asset_deed.objects.filter(amp_code__icontains=search_text ) | \
        Asset_deed.objects.filter(prov_code__icontains=search_text )

    # loop data model Asset_deed
    index = {}
    context_deed = []
    for i in asset_deed_result:
        context_deed.append({
            'deed_no': i.deed_no,
            'tam_code': i.tam_code,
            'amp_code': i.amp_code,
            'prov_code': i.prov_code,
            'rai_number': i.rai_number,
            'ngarn_number': i.ngarn_number,
            'sqw_number': i.sqw_number,
            'parcel_deed_id': i.parcel_deed_id
        })
    # add data model Asset_deed to index
    index['deed'] = context_deed

    context_rent = []
    # filter like data model Asset_rent
    try:
        asset_rent_result = Asset_rent.objects.filter(name_rent__icontains=search_text ) | \
            Asset_rent.objects.filter(tumbon__icontains=search_text ) | \
            Asset_rent.objects.filter(amphoe__icontains=search_text ) | \
            Asset_rent.objects.filter(province__icontains=search_text ) | \
            Asset_rent.objects.filter(deed_no__icontains=search_text )
        # loop data model Asset_rent
        for i in asset_rent_result:
            context_rent.append({
                'number_parcel_rent': i.number_parcel_rent,
                'talk_tunbon': i.talk_tunbon,
                'name_rent': i.name_rent
        })
    except:
        context_rent = []

    index['rent'] = context_rent

    # print(index)
    return JsonResponse(index, safe=False)
    # return HttpResponse(json.dumps(index), content_type='application/json')

# ดึงข้อมูลจาก id เพื่อนำไปแสดง
@login_required
def Get_geom_search_id(request, type=None , get_geom_search_id=None):
    # get geojson data model Asset_deed or Asset_rent
    context = []
    # ถ้าส่งเป็นแปลงโฉนด
    if type == 'deed':
        queryset = Parcel_deed.objects.filter(id=get_geom_search_id)
        geojson = {
            'type': 'FeatureCollection',
            'features': [
                {
                    'type': 'Feature',
                    'geometry': json.loads(x.geom.geojson),
                    'properties': {
                        'id': x.id
                    }
                } for x in queryset
            ]
        }
        context.append(geojson)
        context.append(type)
    # ถ้าส่งเป็นแปลงเช่า


    return JsonResponse(context, safe=False)
# End ดึงข้อมูลจาก id เพื่อนำไปแสดง


# ข้อความบนแผน
def Case_text_map(request):
    # Get POST data
    data = request.POST.copy()
    id_time_line = data.get('id_time_line')
    # ถ้า case เป็น เพิ่มข้อมูล
    if data.get('case') == 'add':
        lat = data.get('lat')
        lng = data.get('lng')
        content = data.get('content')
        type=data.get('type')
        # insert data model Text_map
        text_map = Text_map()
        text_map.time_line_asset_rent_id = id_time_line
        text_map.content = content
        text_map.geom = GEOSGeometry(f"POINT({lng} {lat})", srid=4326)
        text_map.type = type
        text_map.rotate = 0
        text_map.pane_level = 951
        text_map.code_color = '#000000'
        text_map.size = 21
        text_map.save()
        # get data model Text_map as geojson last insert data limit 1 and type = type
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type=type).order_by('-id')[:1]
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "content": x.content,
                        'rotate': x.rotate,
                        'pane_level': x.pane_level,
                        'code_color': x.code_color,
                        'size': x.size,
                        'type': x.type
                    }
                } for x in text_map
            ]
        }
        return JsonResponse(text_map_geojson, safe=False)
    # ถ้า case เป็น แสดงข้อมูล
    elif data.get('case') == 'show':
        id_time_line = data.get('id_time_line')
        # get data model Text_map where type = 'text_map' or type = 'building_text_map'
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type__in=['text_map', 'building_text_map'])
        # Create a GeoJSON object for Leaflet
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "content": x.content,
                        'rotate': x.rotate,
                        'pane_level': x.pane_level,
                        'code_color': x.code_color,
                        'size': x.size,
                        'type': x.type
                    }
                } for x in text_map
            ]
        }
        return JsonResponse(text_map_geojson, safe=False)

    #  แสดงเฉพาะ text_map แผนที่ฐาน
    elif data.get('case') == 'show_text_map':
        id_time_line = data.get('id_time_line')
        # get data model Text_map where type = 'text_map'
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type='text_map')
        # Create a GeoJSON object for Leaflet
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "content": x.content,
                        'rotate': x.rotate,
                        'pane_level': x.pane_level,
                        'code_color': x.code_color,
                        'size': x.size,
                        'type': x.type
                    }
                } for x in text_map
            ]
        }
        return JsonResponse(text_map_geojson, safe=False)

    #  แสดงเฉพาะ building_text_map แปลง
    elif data.get('case') == 'show_building_text_map':
        id_time_line = data.get('id_time_line')
        # get data model Text_map where type = 'building_text_map'
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type='building_text_map')
        # Create a GeoJSON object for Leaflet
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(x.geom.geojson),
                    "properties": {
                        "id": x.id,
                        "content": x.content,
                        'rotate': x.rotate,
                        'pane_level': x.pane_level,
                        'code_color': x.code_color,
                        'size': x.size,
                        'type': x.type
                    }
                } for x in text_map
            ]
        }
        return JsonResponse(text_map_geojson, safe=False)

    # ถ้า case เป็น ย้ายข้อมูล
    elif data.get('case') == 'move':
        id_text_map = data.get('id_text_map')
        lat = data.get('lat')
        lng = data.get('lng')
        # update data model Text_map
        text_map = Text_map.objects.get(id=id_text_map)
        text_map.geom = GEOSGeometry(f"POINT({lng} {lat})", srid=4326)
        text_map.save()
        return HttpResponse('update')

    # ถ้า case เป็น change_content
    elif data.get('case') == 'change_content':
        id_text_map = data.get('id_text_map')
        content = data.get('content')
        # update data model Text_map
        text_map = Text_map.objects.get(id=id_text_map)
        text_map.content = content
        text_map.save()
        return HttpResponse('update')

    # ถ้า case เป็น change_size
    elif data.get('case') == 'change_size':
        id_text_map = data.get('id_text_map')
        size = data.get('size')
        # update data model Text_map
        text_map = Text_map.objects.get(id=id_text_map)
        text_map.size = size
        text_map.save()
        return HttpResponse('update')

    # ถ้า case เป็น change_rotate
    elif data.get('case') == 'change_rotate':
        id_text_map = data.get('id_text_map')
        rotate = data.get('rotate')
        # update data model Text_map
        text_map = Text_map.objects.get(id=id_text_map)
        text_map.rotate = rotate
        text_map.save()
        return HttpResponse('update')

    # ถ้า case เป็น copy
    elif data.get('case') == 'copy':
        id_time_line = data.get('id_time_line')
        id_text_map = data.get('id_text_map')
        lat = data.get('lat')
        lng = data.get('lng')
        type = data.get('type')
        # get data model Text_map
        text_map_get = Text_map.objects.get(id=id_text_map)
        # insert data model Text_map
        text_map = Text_map()
        text_map.time_line_asset_rent_id = id_time_line
        text_map.content = text_map_get.content
        text_map.geom = GEOSGeometry(f"POINT({lng} {lat})", srid=4326)
        text_map.type = type #ประเภทข้อความบนแผนที่
        text_map.rotate = text_map_get.rotate
        text_map.pane_level = text_map_get.pane_level
        text_map.code_color = text_map_get.code_color
        text_map.size = text_map_get.size
        text_map.save()
        # return id last insert where type = type
        text_map = Text_map.objects.filter(time_line_asset_rent_id=id_time_line, type=type).order_by('-id')[:1]
        text_map = text_map[0]
        # return id last insert
        text_map_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": json.loads(text_map.geom.geojson),
                    "properties": {
                        "id": text_map.id,
                        "content": text_map.content,
                        'rotate': text_map.rotate,
                        'pane_level': text_map.pane_level,
                        'code_color': text_map.code_color,
                        'size': text_map.size,
                        'type': text_map.type
                    }
                }
            ]
        }
        return JsonResponse(text_map_geojson, safe=False)

    # ถ้า case เป็น delete
    elif data.get('case') == 'delete':
        id_text_map = data.get('id_text_map')
        # delete data model Text_map and type = 'text_map' or type = 'building_text_map'
        Text_map.objects.filter(id=id_text_map, type__in=['text_map', 'building_text_map']).delete()
        return HttpResponse('delete')

# End ข้อความบนแผน

# เช็คแปลงเช่า มีสิ่งปลูกสร้าง หรือไม่
@login_required
def Check_intersect_building(request):
    try:
        # Get POST data
        data = request.POST.copy()
        id_timeline = data.get('id_timeline')
        # print('id timeline', id_timeline)

        if not id_timeline:
            return JsonResponse({'error': 'id_timeline is required'}, status=400)

        # get data model Time_line_asset_rent
        time_line_asset_rent = Time_line_asset_rent.objects.get(id=id_timeline)

        # get parcel_rent from time_line_asset_rent
        parcel_rent = time_line_asset_rent.parcel_rent

        # get data model Geom_building that intersects with parcel_rent
        geom_building = Geom_building.objects.filter(geom__intersects=parcel_rent.geom)

        # check count data model Geom_building
        count_geom_building = geom_building.count()

        if count_geom_building > 0:
            return JsonResponse({
                'status': 'intersect',
                'count': count_geom_building,
                'message': f'พบสิ่งปลูกสร้าง {count_geom_building} รายการบนแปลงเช่า'
            })
        else:
            return JsonResponse({
                'status': 'not_intersect',
                'count': 0,
                'message': 'ไม่พบสิ่งปลูกสร้างบนแปลงเช่า'
            })

    except Time_line_asset_rent.DoesNotExist:
        return JsonResponse({'error': 'ไม่พบข้อมูล Time_line_asset_rent'}, status=404)
    except Exception as e:
        print(f'Error in Check_intersect_building: {e}')
        return JsonResponse({'error': 'เกิดข้อผิดพลาดภายในระบบ'}, status=500)

#  End เช็คสิ่งปลูกสร้างบนที่ดิน แปลงเช่า


# get building A3
def Ajax_building_A3(request):
    # Get POST data
    data = request.POST.copy()
    id_timeline = data.get('id_timeline')
    print('id timeline', id_timeline)

    if not id_timeline:
        return JsonResponse({'error': 'id_timeline is required'}, status=400)

    # get data model Time_line_asset_rent
    time_line_asset_rent = Time_line_asset_rent.objects.get(id=id_timeline)

    # get parcel_rent from time_line_asset_rent
    parcel_rent = time_line_asset_rent.parcel_rent
    
    # get data model Geom_building that intersects with parcel_rent
    geom_building = Geom_building.objects.filter(geom__intersects=parcel_rent.geom)

    # Create a GeoJSON object for Leaflet
    geom_building_geojson = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "geometry": json.loads(x.geom.geojson),
                "properties": {
                    "id": x.id
                }
            } for x in geom_building
        ]
    }
    return JsonResponse(geom_building_geojson, safe=False)
# End get building A3



# ผูก Asset_rent กับ Asset_deed
def Mtext_rent_asset(request):
    # ให้ผูกสัญญาเช่าภายในที่ดินโฉนด Input
    find_deed = 'ตามประกาศพระบรมราชโองการ'
    find_no_land = ''
    find_provice = "เพชรบุรี"
    parcel_deed_id = 104
    ###########################################

    # read csv file from static/macth parcel
    csv_file_path = Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'CSV_เพรชบุรี.csv'
    # initializing the titles and rows list


    with open(csv_file_path, mode='r') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        # Initialize an empty list to store the dictionaries
        data_list = []

        # Iterate through each row in the CSV file
        for row in csv_reader:
            # Append each row (as a dictionary) to the list
            data_list.append(row)



    # get shpfile from static/macth parcel
    shpfile = Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'text_parcel_thai.shp'

    if not shpfile.exists():
        return HttpResponse(f'File not found: {shpfile}', status=404)

    # อ่านข้อมูลจากไฟล์ shapefile
    gdf = gpd.read_file(shpfile)

    # convert utm to wgs84
    gdf = gdf.to_crs(epsg=4326)

    # get geometry data from gdf
    gdf['geom'] = gdf['geometry'].apply(lambda x: x.wkt)

    # convert POINT Z to point 2D
    gdf['geom'] = gdf['geom'].apply(lambda x: x.replace('Z', ''))

    # get text data from gdf
    gdf['textstring'] = gdf['TEXTSTRING'].apply(lambda x: x.replace('\n', ' '))

    # write csv file to directory STATICFILES_DIRS[0]
    # gdf.to_csv(Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'text_parcel_thai.csv', index=False)


    # get parcel_deed from parcel_deed_id
    parcel_deed = Parcel_deed.objects.get(id=parcel_deed_id)
    # print(parcel_deed.geom)

    # get parcel_rent intersects with parcel_deed
    sql = """
    SELECT pr.id, ST_AsText(pr.geom) as geom_wkt
    FROM app_parcel_rent as pr
    WHERE ST_Intersects(ST_GeomFromText(%s, 4326),pr.geom);
    """
    with connection.cursor() as cursor:
        cursor.execute(sql, [parcel_deed.geom.wkt])
        result = cursor.fetchall()
        # count data model parcel_rent
        count = len(result)
        # print(count)


    # loop result
    for i in result:
        # i[1] get geom
        # i[0] get id parcel rent

        # loop gdf['textstring']
        for index, row in gdf.iterrows():

            # intersects data model parcel_rent
            id_parcel_rent = i[0]
            geom_parcel_rent = i[1]
            # intersects data model text_parcel_thai with parcel_rent sql
            sql = """
            SELECT ST_Intersects(ST_GeomFromText(%s, 4326), ST_GeomFromText(%s, 4326));
            """
            with connection.cursor() as cursor:
                cursor.execute(sql, [geom_parcel_rent, row['geom']])
                intersects = cursor.fetchone()[0]
                # print(row['textstring'], i[0])
                if intersects == True:
                    # Example usage
                    thai_number = row['textstring']
                    arabic_number = thai_to_arabic(thai_number)
                    # arabic_number to string
                    arabic_number = str(arabic_number)
                    # print('หมายเลขแปลง:',arabic_number,'  ', 'ID แปลงเช่า:', id_parcel_rent)
                    # Print the list of dictionaries

                    # คำนวณเทียบกับตาราง csv เพื่อที่จะเชื่อมข้อมูล
                    for data in data_list:
                        # ค้นหาจากหมายเลขแปลง และ เลขที่ดิน และ จังหวัด อยู่ภายใน 1 โฉนด
                        if arabic_number == data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province'] and '1' == data['count_deed_on_asset']:
                            print(data['name_rent'],data['number_parcel'], arabic_number, data['count_deed_on_asset'])
                            name_rent = data['name_rent']
                            if data['name_rent'] == '':
                                name_rent = '-'
                            date_add = datetime.now()
                            # เพิ่มลงฐานข้อมูล หรือ rewrite ลงฐานข้อมูล
                            add_asset_and_timeline('1deed', id_parcel_rent, name_rent, data['property_type'], data['rate_name'], data['number_parcel'], data['subdistrict_language'], data['subdistrict'], data['county'], data['province'], data['deed'], data['no_land'], data['area_wa'], date_add)
                            # exit loop
                            break
                        # ค้นหาจากหมายเลขแปลง และ เลขที่ดิน และ จังหวัด อยู่ภายใน 2 โฉนด
                        if arabic_number == data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province'] and '2' == data['count_deed_on_asset']:
                            print(data['name_rent'],data['number_parcel'], arabic_number, data['count_deed_on_asset'])
                            # exit loop
                            break
                    # print(row['textstring'], i[0])



            # print(row['textstring'], row['geom'])
        # print(i[0], i[1])



    return HttpResponse('insert data to Point_label')





def Single_text_rent_asset(request):
    # ให้ผูกสัญญาเช่าภายในที่ดินโฉนด Input
    find_deed = '8978'
    find_no_land = '3'
    find_provice = "ฉะเชิงเทรา"
    parcel_deed_id = 92
    ###########################################

    # read csv file from static/macth parcel
    csv_file_path = Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'CSV_ฉะเชิงเทรา.csv'
    # initializing the titles and rows list


    with open(csv_file_path, mode='r') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        # Initialize an empty list to store the dictionaries
        data_list = []

        # Iterate through each row in the CSV file
        for row in csv_reader:
            # Append each row (as a dictionary) to the list
            data_list.append(row)



    # get shpfile from static/macth parcel
    shpfile = Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'single_text_parcel_thai.shp'

    if not shpfile.exists():
        return HttpResponse(f'File not found: {shpfile}', status=404)

    # อ่านข้อมูลจากไฟล์ shapefile
    gdf = gpd.read_file(shpfile)

    # convert utm to wgs84
    gdf = gdf.to_crs(epsg=4326)

    # get geometry data from gdf
    gdf['geom'] = gdf['geometry'].apply(lambda x: x.wkt)

    # convert POINT Z to point 2D
    gdf['geom'] = gdf['geom'].apply(lambda x: x.replace('Z', ''))

    # get text data from gdf
    gdf['textstring'] = gdf['TEXTSTRING'].apply(lambda x: x.replace('\n', ' '))

    # write csv file to directory STATICFILES_DIRS[0]
    # gdf.to_csv(Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'text_parcel_thai.csv', index=False)


    # print(parcel_deed_id)
    # get parcel_deed from parcel_deed_id
    parcel_deed = Parcel_deed.objects.get(id=parcel_deed_id)
    # print(parcel_deed.geom)

    # get parcel_rent intersects with parcel_deed
    sql = """
    SELECT pr.id, ST_AsText(pr.geom) as geom_wkt, ST_Area(ST_Transform(pr.geom, 32647)) as area_sqm
    FROM app_parcel_rent as pr
    WHERE ST_Intersects(ST_GeomFromText(%s, 4326),pr.geom);
    """
    with connection.cursor() as cursor:
        cursor.execute(sql, [parcel_deed.geom.wkt])
        result = cursor.fetchall()
        # count data model parcel_rent
        count = len(result)
        # print(count)


    # loop result loop แปลงเช่า
    for i in result:
        # i[1] get geom
        # i[0] get id parcel rent

        print(i[0])

        id_parcel_rent = i[0]
        geom_parcel_rent = i[1]

        area_sqm = i[2] # พื้นที่เช่า ตรม
        area_sqwa = area_sqm /4 # พื้นที่เช่า ตรว

        area_tolalence = 5 # 5 ตรว

        area_below = area_sqwa - area_tolalence # ถ้าพื้นที่เช่าน้อยกว่า 5 ตรว
        area_above = area_sqwa + area_tolalence # ถ้าพื้นที่เช่ามากกว่า 5 ตรว

        intersects_list_text = []
        intersects_list_geom = []
        # loop gdf['textstring']
        for index, row in gdf.iterrows():
            # intersects data model text_parcel_thai with parcel_rent sql
            sql = """
            SELECT ST_Intersects(ST_GeomFromText(%s, 4326), ST_GeomFromText(%s, 4326));
            """
            with connection.cursor() as cursor:
                cursor.execute(sql, [geom_parcel_rent,row['geom']])
                intersects = cursor.fetchone()[0]
                if intersects == True:
                    # print(row['textstring'])
                    intersects_list_text.append(row['textstring'])
                    # append row['geom'] to intersects_list_geom
                    intersects_list_geom.append(row['geom'])
                else:
                    continue
        # count intersects_list_text
        count_intersects_list_text = len(intersects_list_text)
        print(count_intersects_list_text)

        text_number_nonslash = ''

        if count_intersects_list_text == 0:
            continue
        if count_intersects_list_text == 1:
            # loop intersects_list_text and intersects_list_geom
            for text, geom in zip(intersects_list_text, intersects_list_geom):
                thai_number = text
                arabic_number = thai_to_arabic(thai_number)
                # arabic_number = str(arabic_number)
                print('number:',arabic_number)
                text_number = arabic_number
                # print('Single:',arabic_number, 'geom:', geom)
                # print(geom)

        elif count_intersects_list_text == 2:
            # loop intersects_list_text and intersects_list_geom
            for text, geom in zip(intersects_list_text, intersects_list_geom):
                thai_number = text
                arabic_number = thai_to_arabic(thai_number)
                # arabic_number = str(arabic_number)
                print('Multi:', arabic_number, 'geom:', geom)

                # text next
                thai_number_next = intersects_list_text[1]
                arabic_number_next = thai_to_arabic(thai_number_next)
                # arabic_number_next = str(arabic_number_next)

                try:
                    # geom next
                    geom_next = intersects_list_geom[1].split(' ')
                    # geom split this
                    geom = geom.split(' ')
                    lat_this = geom[3]
                    lat_next = geom_next[3]
                    if lat_this > lat_next:
                        text_number = arabic_number + '/' + arabic_number_next
                        text_number_nonslash = arabic_number + arabic_number_next
                        #
                        print(text_number)
                        break
                    else:
                        text_number = arabic_number_next + '/' + arabic_number
                        text_number_nonslash = arabic_number_next + arabic_number
                        print(text_number)
                        break
                except:
                    break



        # คำนวณเทียบกับตาราง csv เพื่อที่จะเชื่อมข้อมูล
        for data in data_list:

            if data['area_wa'] == '':
                data['area_wa'] = '0'
            # ค้นหาจากหมายเลขแปลง และ เลขที่ดิน และ จังหวัด อยู่ภายใน 1 โฉนด ไม่มี  slash
            if text_number == data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province'] and '1' == data['count_deed_on_asset']:
                print(data['name_rent'],data['number_parcel'], text_number, data['count_deed_on_asset'], float(data['area_wa']), area_below, area_above)

                # ถ้าพื้นที่เช่าน้อยกว่า 5 ตรว และ มากกว่า 5 ตรว
                if float(data['area_wa']) > area_below  and float(data['area_wa']) < area_above:

                    name_rent = data['name_rent']
                    if data['name_rent'] == '':
                        name_rent = '-'
                    date_add = datetime.now()
                    # เพิ่มลงฐานข้อมูล หรือ rewrite ลงฐานข้อมูล
                    add_asset_and_timeline('1deed', id_parcel_rent, name_rent, data['property_type'], data['rate_name'], data['number_parcel'], data['subdistrict_language'], data['subdistrict'], data['county'], data['province'], data['deed'], data['no_land'], data['area_wa'], date_add)
                    print('อยู่ภายใน 1 โฉนด ไม่มี  slash')

                name_rent = data['name_rent']
                if data['name_rent'] == '':
                    name_rent = '-'
                date_add = datetime.now()
                # เพิ่มลงฐานข้อมูล หรือ rewrite ลงฐานข้อมูล
                add_asset_and_timeline('1deed', id_parcel_rent, name_rent, data['property_type'], data['rate_name'], data['number_parcel'], data['subdistrict_language'], data['subdistrict'], data['county'], data['province'], data['deed'], data['no_land'], data['area_wa'], date_add)
                print('อยู่ภายใน 1 โฉนด ไม่มี  slash')
                # exit loop
                break
            if text_number_nonslash != '':

                # ค้นหาจากหมายเลขแปลง และ เลขที่ดิน และ จังหวัด อยู่ภายใน 1 โฉนด มี  slash
                if text_number_nonslash == data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province'] and '1' == data['count_deed_on_asset']:
                    print(data['name_rent'],data['number_parcel'], text_number_nonslash, data['count_deed_on_asset'], float(data['area_wa']), area_below, area_above)
                    # ถ้าพื้นที่เช่าน้อยกว่า 5 ตรว และ มากกว่า 5 ตรว
                    if float(data['area_wa']) > area_below  and float(data['area_wa']) < area_above:

                        name_rent = data['name_rent']
                        if data['name_rent'] == '':
                            name_rent = '-'
                        date_add = datetime.now()
                        # เพิ่มลงฐานข้อมูล หรือ rewrite ลงฐานข้อมูล
                        add_asset_and_timeline('1deed', id_parcel_rent, name_rent, data['property_type'], data['rate_name'], data['number_parcel'], data['subdistrict_language'], data['subdistrict'], data['county'], data['province'], data['deed'], data['no_land'], data['area_wa'], date_add)
                        print('อยู่ภายใน 1 โฉนด มี  slash')

                    name_rent = data['name_rent']
                    if data['name_rent'] == '':
                        name_rent = '-'
                    date_add = datetime.now()
                    # เพิ่มลงฐานข้อมูล หรือ rewrite ลงฐานข้อมูล
                    add_asset_and_timeline('1deed', id_parcel_rent, name_rent, data['property_type'], data['rate_name'], data['number_parcel'], data['subdistrict_language'], data['subdistrict'], data['county'], data['province'], data['deed'], data['no_land'], data['area_wa'], date_add)
                    print('อยู่ภายใน 1 โฉนด มี  slash')
                    # exit loop
                    break

            # ค้นหาจากหมายเลขแปลง และ เลขที่ดิน และ จังหวัด อยู่ภายใน 2 โฉนด
            if text_number == data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province'] and '2' == data['count_deed_on_asset']:
                print(data['name_rent'],data['number_parcel'], text_number, data['count_deed_on_asset'])
                # exit loop
                break
            # if text_number != data['number_parcel'] and find_deed == data['deed'] and find_no_land == data['no_land'] and find_provice == data['province']:
            #     id_parcel_rent = i[0]
            #     # write to log
            #     with open(Path(settings.STATICFILES_DIRS[0]) / 'macth_parcel' / 'log.txt', 'a') as f:
            #         f.write(f'{find_deed},{find_no_land},{find_provice},{text_number},{id_parcel_rent}\n')


        # # print(row['textstring'], i[0])



    #         # print(row['textstring'], row['geom'])
    #     # print(i[0], i[1])



    return HttpResponse('insert data to Point_label')





def add_asset_and_timeline(status, id_parcel_rent, name_rent=None, prom_type=None, rate_type=None, number_parcel_rent=None, talk_tunbon=None, tumbon=None, amphoe=None, province=None, deed_no=None, land_no=None, area_rent_sqwa=None, sv_datetime=None):
    # ถ้าสัญญาเช่า มี 1 โฉนด
    if status == '1deed':
        # select id_parcel_rent on model time_line_asset_rent
        time_line_asset_rent = Time_line_asset_rent.objects.filter(parcel_rent=id_parcel_rent)
        # กรณีได้เพิ่มข้อมูลไปแล้ว
        if time_line_asset_rent:
            # select id_asset_rent on model time_line_asset_rent
            id_asset_rent = time_line_asset_rent.latest('date_add').asset_rent_id
            # modify data model Asset_rent
            asset_rent = Asset_rent.objects.get(id=id_asset_rent)
            asset_rent.name_rent = name_rent
            asset_rent.prom_type = prom_type
            asset_rent.rate_type = rate_type
            asset_rent.number_parcel_rent = number_parcel_rent
            asset_rent.talk_tunbon = talk_tunbon
            asset_rent.tumbon = tumbon
            asset_rent.amphoe = amphoe
            asset_rent.province = province
            asset_rent.deed_no = deed_no
            asset_rent.land_no = land_no
            asset_rent.area_rent_sqwa = area_rent_sqwa
            asset_rent.sv_datetime = sv_datetime
            asset_rent.save()
            print('มีข้อมูล', id_asset_rent)
        # กรณียังไม่ได้เพิ่มข้อมูล
        else:
            # insert data model Asset_rent
            asset_rent = Asset_rent()
            asset_rent.name_rent = name_rent
            asset_rent.prom_type = prom_type
            asset_rent.rate_type = rate_type
            asset_rent.number_parcel_rent = number_parcel_rent
            asset_rent.talk_tunbon = talk_tunbon
            asset_rent.tumbon = tumbon
            asset_rent.amphoe = amphoe
            asset_rent.province = province
            asset_rent.deed_no = deed_no
            asset_rent.land_no = land_no
            asset_rent.area_rent_sqwa = area_rent_sqwa
            asset_rent.sv_datetime = sv_datetime
            asset_rent.save()
            # select last id_asset_rent on model Asset_rent
            id_asset_rent = Asset_rent.objects.latest('id')
            # insert data model Time_line_asset_rent
            time_line_asset_rent = Time_line_asset_rent()
            time_line_asset_rent.parcel_rent_id = id_parcel_rent
            time_line_asset_rent.asset_rent_id = id_asset_rent.id
            # used field
            time_line_asset_rent.used = True
            time_line_asset_rent.type_sv_job = 'สอบเขต'
            time_line_asset_rent.date_add = datetime.now()
            time_line_asset_rent.save()
            print('ไม่มีข้อมูล')


def thai_to_arabic(thai_number):
    # Create a mapping of Thai digits to Arabic digits
    thai_to_arabic_map = {
        '๐': '0',
        '๑': '1',
        '๒': '2',
        '๓': '3',
        '๔': '4',
        '๕': '5',
        '๖': '6',
        '๗': '7',
        '๘': '8',
        '๙': '9',
        ' ': '/'  # Map space to '/'
    }

    # Convert each Thai digit to its corresponding Arabic digit, keep other characters as they are
    arabic_number = ''.join(thai_to_arabic_map.get(digit, digit) for digit in thai_number)

    return arabic_number

# ผูก Asset_rent กับ Asset_deed


#เพิ่มแปลงโฉนด
def Insert_deed_auto(request):
    try:
        # read file geopackage from static/deed_private/deed.gpkg
        gpkg_file_path = Path(settings.STATICFILES_DIRS[0]) / 'deed_private' / 'deed.gpkg'

        # check if file exists
        if not gpkg_file_path.exists():
            return JsonResponse({'error': 'GeoPackage file not found'}, status=404)

        # read file geopackage
        gdf = gpd.read_file(gpkg_file_path)

        # convert to GeoJSON format
        features = []
        for index, row in gdf.iterrows():
            feature = {
                'type': 'Feature',
                'geometry': json.loads(row.geometry.to_json()),
                'properties': {
                    'id': row.get('id', index),
                    # Add other properties from the row, excluding geometry
                    **{col: row[col] for col in gdf.columns if col != 'geometry' and col != 'geom'}
                }
            }
            features.append(feature)

        # create GeoJSON FeatureCollection
        geojson = {
            'type': 'FeatureCollection',
            'features': features
        }

        return JsonResponse(geojson, safe=False)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

