from django.urls import path
from .views import *
urlpatterns = [
    path('', Index, name='index-page'),
    # Ajax โฉนดที่ดิน
    path('ajax_parcel_deed/<str:status_survey_dol>/', Ajax_parcel_deed, name='ajax_parcel_deed'),
    path('ajax_detail_parcel_deed/<int:id_parcel_deed>/', Ajax_detail_parcel_deed, name='ajax_detail_parcel_deed'),
    # Ajax แปลงเช่า
    # path('ajax_parcel_rent/<int:id_parcel_rent>/', Ajax_parcel_rent, name='ajax_parcel_rent'),   
    path('ajax_parcel_rent/', Ajax_parcel_rent, name='ajax_parcel_rent'),   
    path('ajax_detail_parcel_rents/<int:id_parcel_rent>/', Ajax_detail_parcel_rent, name='ajax_detail_parcel_rents'), # รูปแปลงเช่า
    path('ajax_building_on_parcel/', Ajax_building_on_parcel, name='ajax_building_on_parcel-page'), # สิ่งปลูกสร้าง บนแปลงเช่า
    path('ajax_side_parcel/', Ajax_side_parcel, name='ajax_side_parcel-page'), # แปลงข้างเคียง
    # Ajax สิ่งปลูกสร้าง
    path('ajax_building/', Ajax_building, name='ajax_building'),
    # ajax url สำหรับ แสดงข้อมูล โฉนดที่ดิน
    path('search_ajax/<str:search_text>/', Search_ajax, name='search_ajax'),
    path('get_geom_search_id/<str:type>/<int:get_geom_search_id>/', Get_geom_search_id, name='get_geom_search_id'),

    path('editor_page/<int:id_parcel_rent>/', Editor_page, name='editor_page-page'),
    path('editor_page/', Editor_page, name='editor_page-page'),
    path('preview/', LayoutA_3, name='layout_page'),
    path('preview/<int:id_timeline>/', LayoutA_3, name='layout_page'),
    path('ajax_layout_render_parcel/', Ajax_layout_render_parcel, name='ajax_layout_render_parcel-page'),
    path('ajax_layout_render_detail/', Ajax_layout_render_detail, name='ajax_layout_render_detail-page'),

    path('test/', Test, name='test-page'),
    path('job_list/', Job_list, name='job_list-page'),
    
    path('add_deed_parcel', Add_deed_parcel, name='add_deed_parcel-page'),
    

    ###################### Editor ######################
    path('side_plot_lines/', Side_plot_lines_views, name='side_plot_lines-page'),
    path('show_asset_rent/', Show_asset_rent, name='show_asset_rent-page'),
    path('add_asset_rent/', Add_asset_rent, name='add_asset_rent-page'),
    # Ajax_pin_rents
    path('ajax_pin_rents/', Ajax_pin_rents, name='ajax_pin_rents-page'), 
    # Update_scale_map
    path('scale_map/', Scale_map, name='scale_map-page'),
    # Ajax_parcel_number
    path('ajax_parcel_number/', Ajax_parcel_number, name='ajax_parcel_number-page'),
    path('case_text_map/', Case_text_map, name='case_text_map-page'),
    
    # path('signup/', Signup, name='signup-page'),

    ####################### หน้า A3c#########################
    # check intersect สิ่งปลูกสร้าง ที่อยู่บน แปลงเช่า
    path('check_intersect_building/', Check_intersect_building, name='check_intersect_building-page'),
    #get building A3
    path('ajax_building_A3/', Ajax_building_A3, name='ajax_building_A3-page'),




    # จับ ผูกสัญญาเช่า
    path('Mtext_rent_asset/', Mtext_rent_asset, name='Mtext_rent_asset-page'),
    path('single_text_rent_asset/', Single_text_rent_asset, name='Single_text_rent_asset-page'),
    

    # เพิ่มแปลงโฉนด
    path('Insert_deed_auto', Insert_deed_auto, name='Insert_deed_auto-page'),
]